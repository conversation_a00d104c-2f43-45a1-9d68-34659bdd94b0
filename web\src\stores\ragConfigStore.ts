/**
 * RAG配置统一状态管理
 * 用于管理知识库创建流程中的所有配置状态和文件状态
 */

import { ref, computed, reactive, watch, readonly } from 'vue'
import { defineStore } from 'pinia'
import type { FileCleanSegmentConfig, UploadedFile, VectorizationConfig } from '@/types/rag'
import type { SysDictDataVO } from '@/types/system'
import {merge} from 'lodash-es'

// 默认配置
const DEFAULT_SEGMENT_CONFIG: FileCleanSegmentConfig = {
  segmentConfig: {
    type: 'directory',
    directory: {
      name: '目录切分',
      // 目录层级
      level: 3
    },
    natural: {
      name: '自然文段切分',
      // 自然段数配置
      segments: 3
    },
    delimiter: {
      name: '分隔符切分',
      delimiter: '\n\n'
    },
    constantLength: {
      name: '固定长度切分',
      maxLen: 1024,
      overlapLen: 50,
    },
    none: {
      name: '不切分'
    }
  },
  cleaningConfig: {
    removeEmptyLines: false,
    removeExtraSpaces: false,
    removeSpecialChars: false,
    normalizeText: false,
    deleteSymbol: false,
    deleteInlineMedia: false,
    filterKeywords: ''
  }
}

export const useRagConfigStore = defineStore('ragConfig', () => {
  // ==================== 基础状态 ====================

  // 知识库创建流程状态
  const datasetId = ref<string>('')
  const datasetName = ref<string>('')
  const batchId = ref<string>('')
  const uploadedFiles = ref<UploadedFile[]>([])
  const currentStep = ref<number>(1)
  const processResult = ref<any>(null)
  const documentStatusDicts = ref<Record<string, SysDictDataVO>>({})

  // 全局默认配置
  const globalConfig = ref<FileCleanSegmentConfig>({ ...DEFAULT_SEGMENT_CONFIG })

  // 文件特定配置映射 (fileId -> config)
  const fileConfigs = reactive<Map<string, FileCleanSegmentConfig>>(new Map())

  // 当前活动的配置（用于预览等场景）
  const activeConfig = ref<FileCleanSegmentConfig>({ ...DEFAULT_SEGMENT_CONFIG })

  // 当前选中的文件ID
  const activeFileId = ref<string | null>(null)

  // 初始化统计信息
  const uploadStats = ref({
    total: 0,
    completed: 0,
    failed: 0
  })

  // 每页显示50个文件
  const pageSize = ref(50)


  // 配置变更历史（用于撤销/重做）
  const configHistory = ref<Array<{
    timestamp: number
    type: 'global' | 'file'
    fileId?: string
    config: FileCleanSegmentConfig
  }>>([])

  // ==================== 计算属性 ====================

  // 计算总页数
  const getTotalPages = computed(() => Math.ceil(uploadedFiles.value.length / pageSize.value))

  // 获取当前有效配置（文件特定配置优先，否则使用全局配置）
  const currentConfig = computed(() => {
    if (activeFileId.value && fileConfigs.has(activeFileId.value)) {
      return fileConfigs.get(activeFileId.value)!
    }
    return globalConfig.value
  })
  
  // 检查是否有自定义配置
  const hasCustomConfig = computed(() => {
    return activeFileId.value ? fileConfigs.has(activeFileId.value) : false
  })
  
  // 检查配置是否已修改
  const isConfigModified = computed(() => {
    const current = currentConfig.value
    return JSON.stringify(current) !== JSON.stringify(DEFAULT_SEGMENT_CONFIG)
  })
  
  // 获取所有有自定义配置的文件数量
  const customConfigCount = computed(() => {
    return fileConfigs.size
  })

  // 文件相关计算属性
  const uploadedCount = computed(() => {
    return uploadedFiles.value.filter(file => file.uploaded).length
  })

  const uploadingCount = computed(() => {
    return uploadedFiles.value.filter(file => file.uploading).length
  })

  const pendingFiles = computed(() => {
    return uploadedFiles.value.filter(file => !file.uploaded && !file.error && file.file)
  })

  const allFilesProcessed = computed(() => {
    return uploadedFiles.value.length > 0 && uploadedFiles.value.every(file => file.uploaded || file.error)
  })

  const hasUploadingFiles = computed(() => {
    return uploadedFiles.value.some(file => file.uploading)
  })

  const getCurFile = computed(() => {
    return uploadedFiles.value.find(file => file.id === activeFileId.value)
  })

  // 计算属性
  const getTotalSize = computed(() => {
    return uploadedFiles.value.reduce((total: number, file: any) => total + file.size, 0)
  })

  /**
   * 获取状态统计
   */
  const getDocumentStatusStats = computed(() => {

    const stats = Object.values(documentStatusDicts.value).map(status => ({
      ...status,
      count: 0
    }))

    uploadedFiles.value.forEach(file => {
      const status = file.documentStatus || 'waiting'
      const stat = stats.find(s => s.dictValue === status)
      if (stat) stat.count++
    })

    return stats.filter(stat => stat.count > 0)
  })


  // ==================== 核心方法 ====================
  
  /**
   * 更新全局配置
   */
  const updateGlobalConfig = (config: Partial<FileCleanSegmentConfig>) => {
    const newConfig = { ...globalConfig.value, ...config }

    // 记录历史
    addToHistory('global', undefined, globalConfig.value)

    globalConfig.value = newConfig

    // 如果没有活动文件或活动文件没有自定义配置，更新活动配置
    if (!activeFileId.value || !fileConfigs.has(activeFileId.value)) {
      activeConfig.value = { ...newConfig }
    }

    // 全局配置更新后，对于没有自定义配置的文件，它们会自动使用新的全局配置
    // 这是通过currentConfig计算属性实现的，无需额外处理
  }
  
  /**
   * 更新文件特定配置
   */
  const updateFileConfig = (fileId: string, config: Partial<FileCleanSegmentConfig>) => {
    const existingConfig = fileConfigs.get(fileId) || JSON.parse(JSON.stringify(globalConfig.value))
    const newConfig = merge({}, existingConfig, config)

    // 记录历史
    addToHistory('file', fileId, existingConfig)

    fileConfigs.set(fileId, newConfig)

    // 如果是当前活动文件，更新活动配置
    if (activeFileId.value === fileId) {
      activeConfig.value = JSON.parse(JSON.stringify(newConfig))
    }
  }
  
  /**
   * 设置活动文件
   */
  const setActiveFile = (fileId: string | null) => {
    activeFileId.value = fileId
    
    if (fileId) {
      // 更新活动配置为当前文件的配置
      activeConfig.value = {
        segmentConfig: getCurFile.value?.segmentConfig || DEFAULT_SEGMENT_CONFIG.segmentConfig,
        cleaningConfig: getCurFile.value?.cleaningConfig || DEFAULT_SEGMENT_CONFIG.cleaningConfig
      }
    } else {
      // 没有活动文件时使用全局配置
      activeConfig.value = { ...globalConfig.value }
    }
  }
  
  /**
   * 获取文件配置
   */
  const getFileConfig = (fileId: string): FileCleanSegmentConfig => {
    const config = fileConfigs.get(fileId)
    if (config) {
      return JSON.parse(JSON.stringify(config))
    }
    return JSON.parse(JSON.stringify(globalConfig.value))
  }

  /**
   * 获取所有文件的最终配置（用于保存到数据库）
   * 返回格式：{ fileId: config }
   */
  const getAllFileConfigs = (): Record<string, FileCleanSegmentConfig> => {
    const allConfigs: Record<string, FileCleanSegmentConfig> = {}

    uploadedFiles.value.forEach(file => {
      // 每个文件都有配置，要么是自定义的，要么是全局默认的
      allConfigs[file.id] = getFileConfig(file.id)
    })

    return allConfigs
  }

  /**
   * 获取只有自定义配置的文件映射（用于保存到数据库）
   * 只返回被修改过的文档配置，未修改的使用全局配置
   */
  const getCustomFileConfigs = (): Record<string, FileCleanSegmentConfig> => {
    const customConfigs: Record<string, FileCleanSegmentConfig> = {}

    // 只返回有自定义配置的文件
    fileConfigs.forEach((config, fileId) => {
      customConfigs[fileId] = JSON.parse(JSON.stringify(config))
    })

    return customConfigs
  }
  
  /**
   * 删除文件配置
   */
  const removeFileConfig = (fileId: string) => {
    if (fileConfigs.has(fileId)) {
      // 记录历史
      addToHistory('file', fileId, fileConfigs.get(fileId)!)
      
      fileConfigs.delete(fileId)
      
      // 如果删除的是当前活动文件的配置，重置为全局配置
      if (activeFileId.value === fileId) {
        activeConfig.value = { ...globalConfig.value }
      }
    }
  }
  
  /**
   * 重置配置到默认值
   */
  const resetToDefault = (fileId?: string) => {
    if (fileId) {
      // 重置特定文件配置
      if (fileConfigs.has(fileId)) {
        addToHistory('file', fileId, fileConfigs.get(fileId)!)
        fileConfigs.delete(fileId)
      }
      
      if (activeFileId.value === fileId) {
        activeConfig.value = { ...DEFAULT_SEGMENT_CONFIG }
      }
    } else {
      // 重置全局配置
      addToHistory('global', undefined, globalConfig.value)
      globalConfig.value = { ...DEFAULT_SEGMENT_CONFIG }
      
      if (!activeFileId.value || !fileConfigs.has(activeFileId.value)) {
        activeConfig.value = { ...DEFAULT_SEGMENT_CONFIG }
      }
    }
  }
  
  /**
   * 批量更新文件配置
   */
  const batchUpdateFileConfigs = (updates: Array<{ fileId: string; config: Partial<FileCleanSegmentConfig> }>) => {
    updates.forEach(({ fileId, config }) => {
      updateFileConfig(fileId, config)
    })
  }
  
  /**
   * 清空所有配置
   */
  const clearAllConfigs = () => {
    // 记录当前状态到历史
    addToHistory('global', undefined, globalConfig.value)
    fileConfigs.forEach((config, fileId) => {
      addToHistory('file', fileId, config)
    })

    globalConfig.value = { ...DEFAULT_SEGMENT_CONFIG }
    fileConfigs.clear()
    activeConfig.value = { ...DEFAULT_SEGMENT_CONFIG }
    activeFileId.value = null
  }

  // ==================== 知识库创建流程方法 ====================

  /**
   * 初始化知识库创建流程
   */
  const initializeKnowledgeFlow = (id: string, name: string, batch: string) => {
    datasetId.value = id
    datasetName.value = name
    batchId.value = batch
    uploadedFiles.value = []
    currentStep.value = 1
    processResult.value = null
    clearAllConfigs()
  }

  /**
   * 设置上传文件列表
   */
  const setUploadedFiles = (files: UploadedFile[]) => {
    uploadedFiles.value = files

    // 初始化配置
    const updates = files.map(file => ({
      fileId: file.id, // 假设 UploadedFile 里有 id
      config: {
        segmentConfig: file.segmentConfig,
        cleaningConfig: file.cleaningConfig,
      } as Partial<FileCleanSegmentConfig>, // 这里填默认配置
    }))

    batchUpdateFileConfigs(updates)
  }

  /**
   * 添加上传文件
   */
  const addUploadedFiles = (files: UploadedFile[]) => {
    uploadedFiles.value.push(...files)
  }

  /**
   * 更新单个文件状态
   */
  const updateFileStatus = (fileId: string, updates: Partial<UploadedFile>) => {
    try {
      const fileIndex = uploadedFiles.value.findIndex(f => f.id === fileId)
      if (fileIndex !== -1) {
        merge(uploadedFiles.value[fileIndex], updates)
      }
    } catch (error) {
      console.error('更新文件状态时发生错误:', error, {
        fileId,
        updates
      })
      // 重新抛出错误，让Vue的错误处理器能够捕获
      throw error
    }
  }

  /**
   * 移除文件
   */
  const removeFile = (fileId: string) => {
    uploadedFiles.value = uploadedFiles.value.filter(f => f.id !== fileId)
    // 同时移除文件的配置
    removeFileConfig(fileId)
  }

  /**
   * 清空所有文件
   */
  const clearAllFiles = () => {
    uploadedFiles.value = []
    fileConfigs.clear()
  }

  /**
   * 设置当前步骤
   */
  const setCurrentStep = (step: number) => {
    currentStep.value = step
  }

  /**
   * 设置处理结果
   */
  const setProcessResult = (result: any) => {
    processResult.value = result
  }

  /**
   * 重置知识库创建流程
   */
  const resetKnowledgeFlow = () => {
    uploadedFiles.value = []
    currentStep.value = 1
    processResult.value = null
    clearAllConfigs()
  }
  
  // ==================== 历史管理 ====================
  
  /**
   * 添加到历史记录
   */
  const addToHistory = (type: 'global' | 'file', fileId: string | undefined, config: FileCleanSegmentConfig) => {
    configHistory.value.push({
      timestamp: Date.now(),
      type,
      fileId,
      config: { ...config }
    })
    
    // 限制历史记录数量
    if (configHistory.value.length > 50) {
      configHistory.value = configHistory.value.slice(-50)
    }
  }
  
  /**
   * 撤销最后一次操作
   */
  const undo = () => {
    const lastHistory = configHistory.value.pop()
    if (lastHistory) {
      if (lastHistory.type === 'global') {
        globalConfig.value = { ...lastHistory.config }
      } else if (lastHistory.fileId) {
        fileConfigs.set(lastHistory.fileId, { ...lastHistory.config })
      }
      
      // 更新活动配置
      if (activeFileId.value) {
        activeConfig.value = { ...currentConfig.value }
      }
    }
  }
  
  // ==================== 工具方法 ====================
  const getFileTypeIcon = (curFile: UploadedFile) => {
    if (curFile && curFile.docIcon) {
      return curFile.docIcon
    } else {
      return '📃';
    }
  }

  /**
   * 导出配置
   */
  const exportConfigs = () => {
    return {
      globalConfig: { ...globalConfig.value },
      fileConfigs: Object.fromEntries(fileConfigs.entries()),
      timestamp: Date.now()
    }
  }
  
  /**
   * 导入配置
   */
  const importConfigs = (data: {
    globalConfig: FileCleanSegmentConfig
    fileConfigs: Record<string, FileCleanSegmentConfig>
  }) => {
    globalConfig.value = { ...data.globalConfig }
    fileConfigs.clear()
    Object.entries(data.fileConfigs).forEach(([fileId, config]) => {
      fileConfigs.set(fileId, { ...config })
    })
    
    // 更新活动配置
    if (activeFileId.value) {
      activeConfig.value = { ...currentConfig.value }
    }
  }

  /**
   * 设置字典数据
   */
  const setdocumentStatusDicts = (dicts: Record<string, SysDictDataVO>) => {
    documentStatusDicts.value = dicts
  }

  // 索引状态相关方法
  const getDocumentStatusText = (status: string | undefined) => {
    if (status && documentStatusDicts.value && documentStatusDicts.value[status]) {
      return documentStatusDicts.value[status].dictLabel
    } else {
      return status
    }
  }

  // 获取文件类型样式
  const getDocumentStatusClass = (status: string | undefined) => {
    if (status && documentStatusDicts.value && documentStatusDicts.value[status]) {
      return documentStatusDicts.value[status].listClass
    } else {
      return ''
    }
  }
  
  return {
    // 知识库创建流程状态
    datasetId: readonly(datasetId),
    datasetName: readonly(datasetName),
    batchId: readonly(batchId),
    uploadedFiles: uploadedFiles,
    currentStep: readonly(currentStep),
    processResult: readonly(processResult),
    documentStatusDicts: readonly(documentStatusDicts),
    pageSize: pageSize,
    uploadStats: uploadStats,

    // 配置状态
    globalConfig: globalConfig,
    fileConfigs: readonly(fileConfigs),
    activeConfig,
    activeFileId: readonly(activeFileId),
    configHistory: readonly(configHistory),

    // 计算属性
    currentConfig,
    hasCustomConfig,
    isConfigModified,
    customConfigCount,
    uploadedCount,
    uploadingCount,
    pendingFiles,
    allFilesProcessed,
    hasUploadingFiles,
    getDocumentStatusText,
    getDocumentStatusClass,
    getDocumentStatusStats,
    getFileTypeIcon,
    getTotalSize,
    getTotalPages,
    getCurFile,

    // 配置方法
    updateGlobalConfig,
    updateFileConfig,
    setActiveFile,
    getFileConfig,
    getAllFileConfigs,
    getCustomFileConfigs,
    removeFileConfig,
    resetToDefault,
    batchUpdateFileConfigs,
    clearAllConfigs,
    undo,
    exportConfigs,
    importConfigs,
    setdocumentStatusDicts,

    // 知识库创建流程方法
    initializeKnowledgeFlow,
    setUploadedFiles,
    addUploadedFiles,
    updateFileStatus,
    removeFile,
    clearAllFiles,
    setCurrentStep,
    setProcessResult,
    resetKnowledgeFlow,

    // 常量
    DEFAULT_SEGMENT_CONFIG: readonly(DEFAULT_SEGMENT_CONFIG)
  }
})

// 类型导出
export type RagConfigStore = ReturnType<typeof useRagConfigStore>
