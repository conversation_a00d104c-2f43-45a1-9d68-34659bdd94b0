/**
 * RAG配置事件管理
 * 统一处理配置相关的事件，减少组件间的直接耦合
 */

import { ref, computed, readonly } from 'vue'
import { useRagConfigStore } from '@/stores/ragConfigStore'
import type { FileCleanSegmentConfig, UploadedFile } from '@/types/rag'

// 事件类型定义
export interface RagConfigEvents {
  // 配置变更事件
  'config:changed': {
    type: 'global' | 'file'
    fileId?: string
    config: FileCleanSegmentConfig
    source: string // 事件来源组件
  }
  
  // 文件选择事件
  'file:selected': {
    fileId: string
    file: UploadedFile
    source: string
  }
  
  // 配置重置事件
  'config:reset': {
    type: 'global' | 'file'
    fileId?: string
    source: string
  }
  
  // 预览事件
  'preview:open': {
    fileId: string
    file: UploadedFile
    source: string
  }
  
  'preview:close': {
    source: string
  }
  
  // 处理流程事件
  'process:start': {
    config: FileCleanSegmentConfig
    files: UploadedFile[]
    source: string
  }
  
  'process:complete': {
    result: any
    source: string
  }
}

// 事件监听器类型
type EventListener<T = any> = (data: T) => void
type EventListeners = Map<string, Set<EventListener>>

/**
 * RAG配置事件管理组合式函数
 */
export function useRagConfigEvents() {
  const configStore = useRagConfigStore()
  
  // 事件监听器存储
  const eventListeners: EventListeners = new Map()
  
  // 当前处理状态
  const isProcessing = ref(false)
  const processProgress = ref(0)
  const currentProcessingFile = ref('')
  
  // ==================== 事件发射 ====================
  
  /**
   * 发射事件
   */
  const emit = <K extends keyof RagConfigEvents>(
    eventName: K,
    data: RagConfigEvents[K]
  ) => {
    const listeners = eventListeners.get(eventName)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`Error in event listener for ${eventName}:`, error)
        }
      })
    }
  }
  
  /**
   * 监听事件
   */
  const on = <K extends keyof RagConfigEvents>(
    eventName: K,
    listener: EventListener<RagConfigEvents[K]>
  ) => {
    if (!eventListeners.has(eventName)) {
      eventListeners.set(eventName, new Set())
    }
    eventListeners.get(eventName)!.add(listener)
    
    // 返回取消监听的函数
    return () => {
      const listeners = eventListeners.get(eventName)
      if (listeners) {
        listeners.delete(listener)
        if (listeners.size === 0) {
          eventListeners.delete(eventName)
        }
      }
    }
  }
  
  /**
   * 一次性监听事件
   */
  const once = <K extends keyof RagConfigEvents>(
    eventName: K,
    listener: EventListener<RagConfigEvents[K]>
  ) => {
    const wrappedListener = (data: RagConfigEvents[K]) => {
      listener(data)
      off(eventName, wrappedListener)
    }
    return on(eventName, wrappedListener)
  }
  
  /**
   * 取消监听事件
   */
  const off = <K extends keyof RagConfigEvents>(
    eventName: K,
    listener: EventListener<RagConfigEvents[K]>
  ) => {
    const listeners = eventListeners.get(eventName)
    if (listeners) {
      listeners.delete(listener)
      if (listeners.size === 0) {
        eventListeners.delete(eventName)
      }
    }
  }
  
  /**
   * 清除所有监听器
   */
  const clearAllListeners = () => {
    eventListeners.clear()
  }
  
  // ==================== 高级事件处理 ====================
  
  /**
   * 处理文件选择事件
   */
  const handleFileSelect = (file: UploadedFile, source: string) => {
    configStore.setActiveFile(file.id)
    emit('file:selected', {
      fileId: file.id,
      file,
      source
    })
  }
  
  /**
   * 处理配置重置事件
   */
  const handleConfigReset = (source: string, fileId?: string) => {
    configStore.resetToDefault(fileId)
    emit('config:reset', {
      type: fileId ? 'file' : 'global',
      fileId,
      source
    })
  }
  
  /**
   * 处理预览打开事件
   */
  const handlePreviewOpen = (file: UploadedFile, source: string) => {
    configStore.setActiveFile(file.id)
    emit('preview:open', {
      fileId: file.id,
      file,
      source
    })
  }
  
  /**
   * 处理预览关闭事件
   */
  const handlePreviewClose = (source: string) => {
    emit('preview:close', { source })
  }
  
  /**
   * 处理处理流程开始事件
   */
  const handleProcessStart = (
    config: FileCleanSegmentConfig,
    files: UploadedFile[],
    source: string
  ) => {
    isProcessing.value = true
    processProgress.value = 0
    currentProcessingFile.value = ''
    
    emit('process:start', {
      config,
      files,
      source
    })
  }
  
  /**
   * 处理处理流程完成事件
   */
  const handleProcessComplete = (result: any, source: string) => {
    isProcessing.value = false
    processProgress.value = 100
    currentProcessingFile.value = ''
    
    emit('process:complete', {
      result,
      source
    })
  }
  
  // ==================== 批量事件处理 ====================
  
  /**
   * 批量更新文件配置
   */
  const batchUpdateConfigs = (
    updates: Array<{ fileId: string; config: Partial<FileCleanSegmentConfig> }>,
    source: string
  ) => {
    configStore.batchUpdateFileConfigs(updates)
    
    // 为每个更新发射事件
    updates.forEach(({ fileId, config }) => {
      emit('config:changed', {
        type: 'file',
        fileId,
        config: configStore.getFileConfig(fileId),
        source
      })
    })
  }
  
  // ==================== 计算属性 ====================
  
  const hasActiveListeners = computed(() => {
    return eventListeners.size > 0
  })
  
  const listenerCount = computed(() => {
    let count = 0
    eventListeners.forEach(listeners => {
      count += listeners.size
    })
    return count
  })
  
  // ==================== 调试工具 ====================
  
  /**
   * 获取所有事件监听器信息（调试用）
   */
  const getListenerInfo = () => {
    const info: Record<string, number> = {}
    eventListeners.forEach((listeners, eventName) => {
      info[eventName] = listeners.size
    })
    return info
  }
  
  /**
   * 发射测试事件（调试用）
   */
  const emitTestEvent = (eventName: string, data: any) => {
    if (import.meta.env.DEV) {
      emit(eventName as keyof RagConfigEvents, data)
    }
  }

  return {
    // 基础事件方法
    emit,
    on,
    once,
    off,
    clearAllListeners,
    
    // 高级事件处理
    handleFileSelect,
    handleConfigReset,
    handlePreviewOpen,
    handlePreviewClose,
    handleProcessStart,
    handleProcessComplete,
    batchUpdateConfigs,
    
    // 状态
    isProcessing: readonly(isProcessing),
    processProgress: readonly(processProgress),
    currentProcessingFile: readonly(currentProcessingFile),
    
    // 计算属性
    hasActiveListeners,
    listenerCount,
    
    // 调试工具
    getListenerInfo,
    emitTestEvent,
    
    // Store 引用
    configStore
  }
}

// 全局事件总线实例（单例）
let globalEventBus: ReturnType<typeof useRagConfigEvents> | null = null

/**
 * 获取全局事件总线实例
 */
export function useGlobalRagEvents() {
  if (!globalEventBus) {
    globalEventBus = useRagConfigEvents()
  }
  return globalEventBus
}

// 类型导出
export type RagConfigEventBus = ReturnType<typeof useRagConfigEvents>
