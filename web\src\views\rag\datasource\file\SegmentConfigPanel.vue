<template>
  <div class="border border-gray-200 border-solid rounded-md">
    <div class="flex h-11 justify-between items-center px-4 py-1 bg-[#f9fafb] border-b border-solid border-gray-200 rounded-tl-md rounded-tr-md ">
      <div>
        <i class="fas fa-kitchen-set"></i>
        <span class="pl-2">文件分段配置</span>
        <span class="total-size">(当前：<span class="text-blue-500">{{ configStore.getCurFile ? configStore.getCurFile.name :  '全局默认配置'}}</span>)</span>
      </div>
      <div class="flex justify-center items-center gap-2">
        <button class="border border-gray-300 border-solid rounded-md text-sm px-2 py-1 hover:bg-gray-100" @click="configStore.setActiveFile(null)">返回默认配置</button>
        <button class="border border-blue-300 border-solid rounded-md text-sm px-2 py-1 bg-blue-200 hover:bg-blue-100" @click="saveConfig">保存</button>
      </div>
    </div>
    <div class="p-5">
      <div class="basis-2/5 flex flex-col gap-5">
        <!-- 分段设置 -->
        <div class="config-section">
          <div class="config-header">
            <h4 class="config-title">
              <i class="fas fa-cut text-blue-500"></i>
              分段设置
            </h4>
            <div class="grid grid-cols-5 gap-2 text-center text-sm">
              <span class="py-1 px-2 bg-gray-100 rounded-md cursor-pointer hover:bg-blue-100" :style="{backgroundColor: localConfig.segmentConfig.type === 'constantLength' ? '#dbeafe' : ''}" @click="changeSegmentType('constantLength')">固定长度切分</span>
              <span class="py-1 px-2 bg-gray-100 rounded-md cursor-pointer hover:bg-blue-100" :style="{backgroundColor: localConfig.segmentConfig.type === 'directory' ? '#dbeafe' : ''}" @click="changeSegmentType('directory')">目录切分</span>
              <span class="py-1 px-2 bg-gray-100 rounded-md cursor-pointer hover:bg-blue-100" :style="{backgroundColor: localConfig.segmentConfig.type === 'natural' ? '#dbeafe' : ''}" @click="changeSegmentType('natural')">自然文段切分</span>
              <span class="py-1 px-2 bg-gray-100 rounded-md cursor-pointer hover:bg-blue-100" :style="{backgroundColor: localConfig.segmentConfig.type === 'delimiter' ? '#dbeafe' : ''}" @click="changeSegmentType('delimiter')">分隔符切分</span>
              <span class="py-1 px-2 bg-gray-100 rounded-md cursor-pointer hover:bg-blue-100" :style="{backgroundColor: localConfig.segmentConfig.type === 'none' ? '#dbeafe' : ''}" @click="changeSegmentType('none')">不切分</span>
            </div>
          </div>
          <div v-if="localConfig.segmentConfig.type === 'directory'">
            <span class="unit-text">目录层级：</span>
            <input
              type="number"
              v-model="localConfig.segmentConfig.natural.segments"
              min="1"
              max="4"
              class="config-input"
            >
            <div class="config-hint">
              根据目录层级，切分指定层级的目录为分段标准, 最大层级不能超过4
            </div>
          </div>
          <div v-if="localConfig.segmentConfig.type === 'natural'">
            <span class="unit-text">自然段数：</span>
            <input
              type="number"
              v-model="localConfig.segmentConfig.natural.segments"
              min="1"
              max="5"
              class="config-input"
            >
            <div class="config-hint">
              根据文本自然段顺序，切分指定的段数作为分段标准，最大段数不能超过5
            </div>
          </div>
          <div class="config-group flex flex-col gap-1" v-if="localConfig.segmentConfig.type === 'delimiter'">
            <div class="flex justify-between items-center">
              <label class="config-label">分段标识符</label>
              <div class="grid grid-cols-5 gap-2 text-center items-center text-sm">
                <span class="config-hint">快捷选择：</span>
                <span class="py-1 px-2 bg-gray-100 rounded-md cursor-pointer hover:bg-blue-100" :style="{backgroundColor: localConfig.segmentConfig.delimiter.delimiter === '\n\n' ? '#dbeafe' : ''}" @click="changeDelimiter('\n\n')">换行符</span>
                <span class="py-1 px-2 bg-gray-100 rounded-md cursor-pointer hover:bg-blue-100" :style="{backgroundColor: localConfig.segmentConfig.delimiter.delimiter === '##' ? '#dbeafe' : ''}" @click="changeDelimiter('##')">##</span>
                <span class="py-1 px-2 bg-gray-100 rounded-md cursor-pointer hover:bg-blue-100" :style="{backgroundColor: localConfig.segmentConfig.delimiter.delimiter === '//' ? '#dbeafe' : ''}" @click="changeDelimiter('//')">//</span>
                <span class="py-1 px-2 bg-gray-100 rounded-md cursor-pointer hover:bg-blue-100" :style="{backgroundColor: localConfig.segmentConfig.delimiter.delimiter === '@@' ? '#dbeafe' : ''}" @click="changeDelimiter('@@')">@@</span>
              </div>
            </div>
            <div>
              <input
                type="text"
                v-model="localConfig.segmentConfig.delimiter.delimiter"
                placeholder="输入分段标识符，如 \n\n"
                class="config-input-text"
              >
              <div class="config-hint">
                通用文本分段标识，检索和召回的块基相同的
              </div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-10" v-if="localConfig.segmentConfig.type === 'constantLength'">
            <div class="config-group flex flex-col gap-2">
              <div class="flex items-center gap-3">
                <label class="text-sm font-semibold text-gray-500">分段最大长度</label>
                <div>
                  <input
                    type="number"
                    v-model="localConfig.segmentConfig.constantLength.maxLen"
                    min="200"
                    max="2000"
                    class="config-input"
                  >
                  <span class="unit-text">字符</span>
                </div>
              </div>
              <div class="input-with-slider">
                <input
                  type="range"
                  v-model="localConfig.segmentConfig.constantLength.maxLen"
                  min="200"
                  max="2000"
                  step="100"
                  class="config-slider"
                >
              </div>
            </div>

            <div class="config-group flex flex-col gap-2">
              <div class="flex items-center gap-3">
                <label class="text-sm font-semibold text-gray-500">分段重叠长度</label>
                <div>
                  <input
                    type="number"
                    v-model="localConfig.segmentConfig.constantLength.overlapLen"
                    :min="0"
                    class="config-input"
                  >
                  <span class="unit-text">字符</span>
                  <span class="text-xs text-gray-400">最大长度为 {{ Math.floor(localConfig.segmentConfig.constantLength.maxLen * 0.5) }}</span>
                </div>
              </div>
              <div class="input-with-slider">
                <input
                  type="range"
                  v-model="localConfig.segmentConfig.constantLength.overlapLen"
                  :min="0"
                  :max="Math.floor(localConfig.segmentConfig.constantLength.maxLen * 0.5)"
                  step="10"
                  class="config-slider"
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 清洗配置 -->
        <div class="config-section">
          <div class="config-header">
            <h4 class="config-title">
              <i class="fas fa-broom"></i>
              清洗配置
            </h4>
          </div>

          <div class="config-group">
            <div class="checkbox-group">
              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="localConfig.cleaningConfig.removeEmptyLines"
                >
                <span>移除空行</span>
                <div class="option-desc">删除文档中的空白行</div>
              </label>

              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="localConfig.cleaningConfig.removeExtraSpaces"
                >
                <span>移除多余空格</span>
                <div class="option-desc">将多个连续空格合并为单个空格</div>
              </label>

              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="localConfig.cleaningConfig.removeSpecialChars"
                >
                <span>移除特殊字符</span>
                <div class="option-desc">删除非文本字符和控制字符</div>
              </label>

              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="localConfig.cleaningConfig.normalizeText"
                >
                <span>文本标准化</span>
                <div class="option-desc">统一字符编码和格式</div>
              </label>

              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="localConfig.cleaningConfig.deleteSymbol"
                >
                <span>删除所有 URL 和电子邮件地址</span>
              </label>

              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="localConfig.cleaningConfig.deleteInlineMedia"
                >
                <span>删除所有文件正文中内嵌的多媒体（图片、视频、音频等）</span>
              </label>
            </div>
          </div>
        </div>

        <!-- 过滤配置 -->
        <div class="config-section">
          <div class="config-header">
            <h4 class="config-title">
              <i class="fas fa-filter"></i>
              过滤配置
            </h4>
          </div>

          <div class="config-group">
            <label class="config-label">过滤关键词 (可选)</label>
            <textarea
              v-model="localConfig.cleaningConfig.filterKeywords"
              placeholder="输入要过滤的关键词，每行一个"
              class="config-textarea"
              rows="3"
            ></textarea>
            <div class="config-hint">
              包含这些关键词的分段将被过滤掉
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineEmits } from 'vue'
import type {FileCleanSegmentConfig, FileCleanSegmentConfigParams} from '@/types/rag'

import { useRagConfigStore } from '@/stores/ragConfigStore'
import { clone } from 'lodash-es'

// 统一状态管理
const configStore = useRagConfigStore()

// 定义事件
const emit = defineEmits(['config-changed', 'action-clicked', 'reset-config'])

// 本地配置
const localConfig = ref<FileCleanSegmentConfig>({
  ...configStore.activeConfig,
})

// 切分方式选择处理
const changeDelimiter = (delimiter: '\n\n' | '##' | '//' | '@@') => {
  localConfig.value.segmentConfig.type = 'delimiter'
  localConfig.value.segmentConfig.delimiter.delimiter = delimiter
}

// 切分方式选择处理
const changeSegmentType = (type: 'directory' | 'natural' | 'delimiter' | 'constantLength' | 'none') => {
  localConfig.value.segmentConfig.type = type
}

// 保存配置
const fileCleanSegmentConfigParams = ref<FileCleanSegmentConfigParams>({
  ...configStore.globalConfig,
  docMetadataMap: {}
})
const saveConfig = () => {
  console.log("===========: ", fileCleanSegmentConfigParams.value)
}

// 监听配置变化
watch(localConfig, (newConfig) => {
  const cloneConfig = clone(newConfig)
  if (configStore.activeFileId) {
    configStore.updateFileConfig(configStore.activeFileId, cloneConfig)
    fileCleanSegmentConfigParams.value.docMetadataMap[configStore.activeFileId] = cloneConfig
  } else {
    fileCleanSegmentConfigParams.value.segmentConfig = cloneConfig.segmentConfig
    fileCleanSegmentConfigParams.value.cleaningConfig = cloneConfig.cleaningConfig
  }
  console.log("===========: ", fileCleanSegmentConfigParams.value)
}, { deep: true })
</script>

<style scoped>

.config-section {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 12px;
  margin-bottom: 16px;
}

.config-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.config-title i {
  color: #3b82f6;
}

.config-group {
  margin-bottom: 16px;
}

.config-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.input-with-slider {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.config-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.config-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.config-input-text {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  font-family: 'Courier New', monospace;
}

.config-input-text:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.unit-text {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
  white-space: nowrap;
}

.config-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

.config-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.config-hint {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.4;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.checkbox-option input[type="checkbox"] {
  margin-top: 2px;
}

.checkbox-option span {
  font-weight: 500;
  color: #374151;
}

.option-desc {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

/* 向量化配置样式 */

.radio-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.radio-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.radio-option input[type="radio"] {
  margin-right: 8px;
  margin-top: 2px;
}

.radio-text {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #374151;
}

.config-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.config-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.config-checkbox {
  margin-right: 8px;
  margin-top: 2px;
}

.weight-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.weight-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weight-label {
  font-size: 13px;
  font-weight: 500;
  color: #475569;
}
</style>
