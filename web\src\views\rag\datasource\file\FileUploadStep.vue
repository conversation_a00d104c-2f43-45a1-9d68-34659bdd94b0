<template>
  <div class="file-upload-step h-full">
    <!-- 文件拖拽上传区域 -->
    <div
      class="upload-area flex-1"
      :class="{
        'drag-over': isDragOver,
        'has-files': configStore.uploadedFiles.length > 0
      }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <div v-if="configStore.uploadedFiles.length === 0" class="upload-placeholder">
        <div class="upload-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div class="upload-text">
          <div class="primary-text">拖拽文件到此处，或点击选择文件</div>
          <div class="secondary-text">支持 PDF、DOC、DOCX、TXT、MD 等格式，单个文件最大 50MB，支持大量文件同时上传</div>
        </div>
        <button class="upload-btn" type="button">
          <i class="fas fa-plus"></i>
          选择文件
        </button>
      </div>

      <div v-else class="upload-summary">
        <div>&nbsp;</div>
        <div class="step-header">
          <h3 class="step-title">
            <i class="fas fa-cloud-upload-alt"></i>
            上传文件源
          </h3>
          <p class="step-description">
            支持拖拽文件到此区域或点击按钮选择文件上传。支持 PDF、DOC、DOCX、TXT、MD 等格式，单个文件最大 50MB，支持大量文件同时上传。
          </p>
        </div>
        <!-- 右侧操作区域 -->
        <div class="upload-summary-actions">
          <!-- 操作按钮组 -->
          <div class="upload-actions-inline">
            <button class="add-more-btn" type="button" @click.stop="triggerFileInput">
              <i class="fas fa-plus"></i>
              添加更多文件
            </button>

            <button
              class="upload-all-btn"
              :class="{
                'uploading': configStore.hasUploadingFiles,
                'auto-mode': true
              }"
              @click="startAutoUpload"
            >
              <i v-if="configStore.hasUploadingFiles" class="fas fa-spinner fa-spin"></i>
              <i v-else-if="configStore.allFilesProcessed" class="fas fa-check"></i>
              <i v-else class="fas fa-robot"></i>
              <span v-if="configStore.hasUploadingFiles">自动上传中...</span>
              <span v-else-if="configStore.allFilesProcessed">全部完成</span>
              <span v-else>自动上传模式 ({{ configStore.pendingFiles.length }})</span>
            </button>
          </div>
        </div>
      </div>
    </div>


    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      accept=".pdf,.doc,.docx,.txt,.md,.rtf"
      style="display: none"
      @change="handleFileSelect"
    >
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { RagAPI } from '@/api/rag'
import type { UploadedFile } from '@/types/rag'
import {v4 as uuidv4 } from 'uuid'
import { useRagConfigStore } from '@/stores/ragConfigStore'

// 统一状态管理
const configStore = useRagConfigStore()

// 响应式数据
const isDragOver = ref(false)
const fileInput = ref<HTMLInputElement>()

// RAG配置信息
const ragConfig = ref<any>(null)
const maxUploadFiles = ref(100) // 默认值，从配置中获取

// 方法
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  // 只有当离开整个拖拽区域时才设置为false
  if (!(e.currentTarget as Element)?.contains(e.relatedTarget as Node)) {
    isDragOver.value = false
  }
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false

  const files = Array.from(e.dataTransfer?.files || [])
  processFiles(files)
}

const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = Array.from(target.files || [])
  processFiles(files)
  target.value = '' // 清空input，允许重复选择同一文件
}

const processFiles = async (files: File[]) => {

  const validFiles = files.filter(file => {
    // 文件大小限制 50MB
    if (file.size > 50 * 1024 * 1024) {
      ElMessage.error(`文件 ${file.name} 超过 50MB 限制`)
      return false
    }

    // 文件类型检查
    const allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'md', 'rtf', 'html', 'csv', 'xlsx', 'pptx']
    const fileType = file.name.split('.').pop()?.toLowerCase()
    if (!fileType || !allowedTypes.includes(fileType)) {
      ElMessage.error(`文件 ${file.name} 格式不支持`)
      return false
    }

    return true
  })

  if (validFiles.length === 0) {
    return
  }

  // 如果文件数量很大，显示确认对话框
  if (validFiles.length > 1000) {
    const confirmed = confirm(`您选择了 ${validFiles.length} 个文件，这可能需要较长时间上传。是否继续？`)
    if (!confirmed) {
      return
    }
  }

  const processedFiles: UploadedFile[] = []
  for (let file of validFiles) {
    // 开始构建文件上传列表
    const processedFile: UploadedFile = {
      id: uuidv4(),
      name: file.name,
      fileSize: file.size,
      docType: file.name.split('.').pop()?.toLowerCase() || '',
      file: file,
      uploading: true,
      uploaded: false,
      progress: 10,
      error: null,
      updateTime: null,
      isModified: false,
      wordCount: 0,
      documentStatus: undefined
    }
    processedFiles.push(processedFile)
  }

  // 添加到统一状态管理
  configStore.addUploadedFiles(processedFiles)

  // 如果当前未上传文件列表为空，则再次启动开始上传，否则新加的文件会自动进入下一批上传中
  startAutoUpload()
}

// 自动上传管理器 - 核心自动上传逻辑
const startAutoUpload = async () => {
  if (!configStore.datasetId) {
    return
  }

  // 当还有未上传的文件时，根据maxUploadFiles分批上传
  const filesToUpload = configStore.pendingFiles
  if (filesToUpload.length === 0) {
    console.log('没有待上传的文件')
    return
  }

  // 当还有未上传的文件时，根据maxUploadFiles分批上传
  await uploadFilesBatch(filesToUpload)

  // 一直迭代上传，直到所有文件上传完成 或有新的文件再次添加
  await startAutoUpload()
}

// 分批上传文件的核心方法
const uploadFilesBatch = async (filesToUpload: any[]) => {
  // 创建FormData
  const formData = new FormData()
  formData.append('datasetId', configStore.datasetId)
  // 用户每次进入文件上传页面时，生成新的批次ID
  formData.append('batchId', configStore.batchId)

  // 获取批次文件列表
  const batchFiles = filesToUpload.slice(0, maxUploadFiles.value)

  batchFiles.forEach(fileItem => {
    // 批次文件填入表单中
    formData.append('files', fileItem.file)

    // 设置文件为上传中状态
    updateFileStatus(fileItem.id, { uploading: true, uploaded: false, progress: 20, error: null })
  })

  try {
    // 调用批量上传API，支持进度回调
    const response = await RagAPI.batchUploadDocuments(formData, (_progress) => {
      // 更新批次中所有文件的进度：上传中
      batchFiles.forEach(fileItem => {
        updateFileStatus(fileItem.id, {
          uploading: true,
          uploaded: false,
          progress: 30,
          error: null
        })
      })
    })

    // 批次上传成功，根据后端返回的每个文件状态来更新
    if (response.success && response.data) {
      // 根据后端返回的数据更新每个文件的状态
      response.data.forEach((backendFile: any) => {
        // 从当前批次中找到对应的文件id
        const fileId = batchFiles.find(fileItem => fileItem.name === backendFile.name)?.id
        if (fileId) {
          if (!backendFile.documentStatus.endsWith("_error")) {
            // 上传成功
            updateFileStatus(fileId, {
              documentId: backendFile.id, // 保存后端文件ID
              uploading: false,
              uploaded: true,
              progress: 100,
              error: null,
              wordCount: backendFile.wordCount,
              documentStatus: backendFile.documentStatus,
              updateTime: backendFile.updateTime,
              docType: backendFile.docType
            })
            configStore.uploadStats.completed++
          } else {
            updateFileStatus(fileId, {
              documentId: backendFile.id, // 保存后端文件ID
              uploading: false,
              uploaded: false,
              progress: 0,
              error: backendFile.uploadError || '上传失败',
              updateTime: backendFile.updateTime
            })
            configStore.uploadStats.failed++
          }
        }
      })

      // 处理没有在后端返回数据中找到的文件（可能上传失败）
      batchFiles.forEach(fileItem => {
        const found = response.data.some((backendFile: any) => backendFile.name === fileItem.name)
        if (!found) {
          updateFileStatus(fileItem.id, {
            uploading: false,
            uploaded: false,
            progress: 0,
            error: '文件未在服务器响应中找到',
            updateTime: null
          })
          configStore.uploadStats.failed++
        }
      })
    } else {
      // 如果整个批次失败，标记所有文件为失败并从已处理集合中移除
      batchFiles.forEach(fileItem => {
        updateFileStatus(fileItem.id, {
          uploading: false,
          uploaded: false,
          progress: 0,
          error: response.message || '批次上传失败',
          updateTime: null
        })
        configStore.uploadStats.failed++
      })
    }
  } catch (error: any) {
    // 批次上传失败
    batchFiles.forEach(fileItem => {
      updateFileStatus(fileItem.id, {
        uploading: false,
        uploaded: false,
        progress: 0,
        error: error.message || '上传失败'
      })
      configStore.uploadStats.failed++
    })
  }
}

// 更新文件状态的辅助方法
const updateFileStatus = (fileId: string, updates: any) => {
  configStore.updateFileStatus(fileId, updates)
}

// 获取RAG配置信息
const loadRagConfig = async () => {
  try {
    const response = await RagAPI.getRagConfig()
    if (response.success && response.data) {
      ragConfig.value = response.data
      maxUploadFiles.value = response.data.limits?.maxUploadFiles || 10
      console.log('RAG配置加载成功:', ragConfig.value)
    }
  } catch (error) {
    console.error('获取RAG配置失败:', error)
    // 使用默认值
    maxUploadFiles.value = 10
  }
}

// 组件挂载时加载配置
onMounted(async () => {
  await loadRagConfig()

  // 加载页面就开始启动文件上传
  if (configStore.datasetId) {
    await startAutoUpload()
  } else {
    console.log('未设置数据集ID，跳过自动上传')
  }
})

</script>

<style scoped>
.file-upload-step {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-header {
  text-align: center;
}

.step-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.step-title i {
  color: #3b82f6;
}

.step-description {
  color: #64748b;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.auto-upload-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #dbeafe;
  border: 1px solid #93c5fd;
  border-radius: 6px;
  color: #2563eb;
  font-size: 13px;
  font-weight: 500;
}

.auto-upload-indicator i {
  font-size: 14px;
}

/* 上传区域 */
.upload-area {
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  padding: 10px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.upload-area:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.upload-area.drag-over {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: scale(1.02);
}

.upload-area.has-files {
  border-style: solid;
  border-color: #10b981;
  background: #f0fdf4;
  flex: 0;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  font-size: 48px;
  color: #94a3b8;
}

.upload-area:hover .upload-icon,
.upload-area.drag-over .upload-icon {
  color: #3b82f6;
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.primary-text {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.secondary-text {
  font-size: 14px;
  color: #6b7280;
}

.upload-btn {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.upload-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.upload-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.summary-info i {
  font-size: 20px;
}

.add-more-btn {
  padding: 8px 16px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.add-more-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* 新的内联布局样式 */
.upload-summary-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.upload-actions-inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-actions-inline .upload-all-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 100px;
  justify-content: center;
}

.upload-actions-inline .upload-all-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.upload-actions-inline .upload-all-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.upload-actions-inline .upload-all-btn.uploading {
  background: #f59e0b;
}

.upload-actions-inline .upload-all-btn.uploading:hover {
  background: #f59e0b;
}


.progress-details span {
  padding: 2px 6px;
  background: #f9fafb;
  border-radius: 3px;
}


/* 自动上传模式按钮样式 */
.upload-all-btn.auto-mode {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  cursor: default;
}

.upload-all-btn.auto-mode.uploading {
  background: #dbeafe;
  color: #2563eb;
  border-color: #93c5fd;
}


.progress-details span {
  padding: 2px 6px;
  background: #f9fafb;
  border-radius: 3px;
}

.file-list-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.modified-badge i {
  font-size: 10px;
}

.file-meta span {
  padding: 2px 6px;
  background: #f3f4f6;
  border-radius: 3px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.preview-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.preview-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-area {
    padding: 24px 16px;
  }

  .upload-summary {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    gap: 12px;
  }

  .upload-summary-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .upload-actions-inline {
    flex-direction: column;
    gap: 8px;
  }

  .upload-actions-inline .add-more-btn,
  .upload-actions-inline .upload-all-btn,
  .upload-actions-inline .retry-btn {
    width: 100%;
    justify-content: center;
  }


}
</style>
