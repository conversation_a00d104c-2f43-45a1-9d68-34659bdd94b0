package com.xhcai.modules.rag.entity.inner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 分段配置内部类
 */
@Data
@Schema(description = "分段配置")
public class SegmentConfig {

    @Schema(description = "分段类型：directory-目录切分， constantLength-固定长度切分，natural-自然文段切分，delimiter-分隔符切分，none-不切分")
    private String type;

    @Schema(description = "目录切分分段配置参数")
    private DirectoryConfig directory;

    @Schema(description = "自然文段切分分段配置参数")
    private NaturalConfig natural;

    @Schema(description = "分隔符切分分段配置参数")
    private DelimiterConfig delimiter;

    @Schema(description = "固定长度切分分段配置参数")
    private ConstantLengthConfig constantLength;

    /**
     * 目录切分配置
     */
    @Schema(description = "目录切分配置")
    @Data
    public static class DirectoryConfig {

        /**
         * 目录层级
         */
        @Schema(description = "目录层级", example = "3")
        private Integer level = 3;
    }

    /**
     * 自然段切分配置
     */
    @Schema(description = "自然段切分配置")
    @Data
    public static class NaturalConfig {

        /**
         * 自然段数
         */
        @Schema(description = "自然段数", example = "3")
        private Integer segments = 3;
    }

    /**
     * 分隔符切分配置
     */
    @Schema(description = "分隔符切分配置")
    @Data
    public static class DelimiterConfig {

        /**
         * 分隔符
         */
        @Schema(description = "分隔符", example = "\\n\\n")
        private String delimiter = "\n\n";
    }

    /**
     * 固定长度切分配置
     */
    @Schema(description = "固定长度切分配置")
    @Data
    public static class ConstantLengthConfig {

        /**
         * 最大长度
         */
        @Schema(description = "最大长度", example = "1024")
        private Integer maxLen = 1024;

        /**
         * 重叠长度
         */
        @Schema(description = "重叠长度", example = "50")
        private Integer overlapLen = 50;
    }
}