<template>
  <div class="min-h-full flex flex-col">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-top flex justify-center items-center gap-3">
        <div class="flex justify-center items-center">
          <button
            @click="goBack"
            class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <div class="breadcrumb">
            <span class="breadcrumb-item">文档处理流程</span>
            <i v-if="knowledgeFlow.datasetName.value" class="fas fa-chevron-right"></i>
            <span v-if="knowledgeFlow.datasetName.value" class="breadcrumb-item current">{{knowledgeFlow.datasetName.value}}</span>
          </div>
        </div>
        <div class="flex-1 text-center flex bg-white gap-10 justify-center">
          <!-- 流程步骤指示器 -->
          <div
            v-for="(step, index) in processSteps"
            :key="index"
            class="step-item"
            :class="{
          active: knowledgeFlow.currentStep.value === index + 1,
          completed: knowledgeFlow.currentStep.value > index + 1,
          disabled: knowledgeFlow.currentStep.value < index + 1
        }"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-desc">{{ step.description }}</div>
            </div>
          </div>
        </div>
        <div class="flex justify-center items-center">
          <!-- 上一步按钮 -->
          <button
            class="prev-step-btn"
            type="button"
            @click="previousStep"
            :disabled="knowledgeFlow.currentStep.value <= 1"
          >
            <i class="fas fa-arrow-left"></i>
            上一步
          </button>
          <!-- 下一步按钮 -->
          <button
            v-if="knowledgeFlow.currentStep.value === 1"
            class="next-step-btn"
            type="button"
            @click="nextStep"
            :disabled="knowledgeFlow.currentStep.value >= 3 || !knowledgeFlow.canProceedToNext.value"
          >
            下一步
            <i class="fas fa-arrow-right"></i>
          </button>
          <button
            v-if="knowledgeFlow.currentStep.value === 2"
            class="next-step-btn"
            type="button"
            @click="startProcess"
            :disabled="processing"
          >
            <i :class="processing ? 'fas fa-spinner fa-spin' : 'fas fa-play'"></i>
            {{ processing ? '处理中...' : '开始处理' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 步骤内容区域 -->
    <div class="flex-1 p-6 overflow-y-auto flex flex-col gap-3">
      <!-- 步骤1: 上传文件源 -->
      <div v-if="knowledgeFlow.currentStep.value === 1" class="step-panel  h-full-auto" :style="{flex: configStore.uploadedFiles.length > 0 ? '0' : '1'}">
        <FileUploadStep />
      </div>

      <!-- 步骤2: 文本分段与清洗 -->
      <div v-if="knowledgeFlow.currentStep.value === 2 && !processing" class="grid grid-cols-2 gap-5">
        <SegmentConfigPanel/>
        <UploadFileList/>
      </div>
      <div v-if="knowledgeFlow.currentStep.value === 3" class="step-panel h-full-auto" :style="{flex: configStore.uploadedFiles.length > 0 ? '0' : '1'}">
        <ProcessCompleteStep />
      </div>
      <div v-if="knowledgeFlow.currentStep.value === 1 && configStore.uploadedFiles.length > 0" class="flex-1">
        <UploadFileList/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import FileUploadStep from '@/views/rag/datasource/file/FileUploadStep.vue'
import ProcessCompleteStep from '@/views/rag/components/ProcessCompleteStep.vue'
import UploadFileList from '@/views/rag/datasource/file/UploadFileList.vue'
import SegmentConfigPanel from '@/views/rag/datasource/file/SegmentConfigPanel.vue'
import { useKnowledgeFlow } from '@/composables/useKnowledgeFlow'
import { useGlobalRagEvents } from '@/composables/useRagConfigEvents'
import DictAPI from '@/api/dict'
import { useRagConfigStore } from '@/stores/ragConfigStore'
import { useAuthStore } from '@/stores/authStore'
import { type BatchSegmentationRequest, RagAPI } from '@/api/rag'
import type {DocumentQueryDTO} from '@/types/rag'
import {v4 as uuidv4 } from 'uuid'

// 统一状态管理
const configStore = useRagConfigStore()
// 使用stores
const authStore = useAuthStore()

// 路由
const router = useRouter()
const route = useRoute()

// 统一状态管理
const knowledgeFlow = useKnowledgeFlow()
const eventBus = useGlobalRagEvents()

// 事件监听器清理函数
const eventCleanupFunctions = ref<Array<() => void>>([])

// 流程步骤
const processSteps = [
  { title: '上传文件源', description: '从文件夹拖拽或点击上传文件' },
  { title: '文本分段与清洗', description: '配置分段与清洗参数' },
  { title: '处理完成', description: '查看处理结果' }
]

// 方法
const goBack = () => {
  router.push({path: `/knowledge/${knowledgeFlow.datasetId.value}/datasource`, query: {name: knowledgeFlow.datasetName.value}})
}

const nextStep = () => {
  knowledgeFlow.nextStep()
}

const previousStep = () => {
  knowledgeFlow.previousStep()
}

// 加载AI提供商字典
const queryDocumentStatusDictData = async () => {
  const response = await DictAPI.getDictDataByType('document_status')
  if (response.success && response.data) {
    configStore.setdocumentStatusDicts(response.data.reduce( (acc, item) =>  {
      acc[item.dictValue] = item
      return acc
    }, {} as Record<string, typeof response.data[number]>))
  } else {
    configStore.setdocumentStatusDicts({} as Record<string, typeof response.data[number]>)
  }
}

const processing = ref(false)
const processProgress = ref(0)
const processedFiles = ref(0)
const totalFiles = ref(0)

// 计算属性
const canProcess = computed(() => {
  return configStore.uploadedFiles.length > 0 && !processing.value
})

const startProcess = async () => {
  if (!canProcess.value) return

  processing.value = true
  processProgress.value = 0
  processedFiles.value = 0
  totalFiles.value = configStore.uploadedFiles.length

  try {
    // 准备批量分段处理请求数据
    const documentIds = configStore.uploadedFiles.map((file: any) => file.documentId)
    const docMetadataMap: Record<string, any> = {}

    configStore.uploadedFiles.forEach((file: any) => {
      docMetadataMap[file.documentId] = file.segmentConfig || {}
    })

    const requestData: BatchSegmentationRequest = {
      documentIds,
      docMetadataMap,
      segmentConfig: configStore.globalConfig
    }

    // 建立SSE连接监听状态更新
    await connectSSE()

    // 等待SSE连接稳定
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log('SSE连接已建立，开始发送分段处理请求')

    // 调用后端批量分段处理接口
    const result = await RagAPI.batchStartSegmentation(requestData)

    if (result.code !== 200) {
      throw new Error(result.message || '批量分段处理启动失败')
    }

    console.log('批量分段处理启动成功:', result)

  } catch (error) {
    console.error('批量分段处理启动失败:', error)
    processing.value = false

    // 显示错误信息并更新文件状态
    configStore.uploadedFiles.forEach((file: any) => {
      // 已分段完成的，保持不变
      if (file.documentStatus !== 'segmented') {
        file.documentStatus = 'segment_error'
        file.processProgress = 0
      }
    })
  }
}

// 设置事件监听器
const setupEventListeners = () => {
  // 监听配置变更事件
  const configChangedCleanup = eventBus.on('config:changed', (data) => {
  })

  // 监听处理完成事件
  const processCompleteCleanup = eventBus.on('process:complete', (data) => {
    // 自动跳转到下一步
    nextStep()
  })

  eventCleanupFunctions.value.push(configChangedCleanup, processCompleteCleanup)
}

// SSE连接对象
let eventSource: EventSource | null = null

// 建立SSE连接 - 使用EventSource实现
const connectSSE = async () => {
  try {
    // 关闭之前的连接
    if (eventSource) {
      eventSource.close()
      eventSource = null
    }

    // 构建SSE URL
    const url = RagAPI.getDocumentStatusSSEUrl()

    // 添加认证参数到URL
    const token = authStore.tokens?.token || localStorage.getItem('token') || ''
    const urlWithAuth = `${url}?token=${encodeURIComponent(token)}`

    console.log('开始建立SSE连接:', url)

    // 创建EventSource连接
    eventSource = new EventSource(urlWithAuth)

    // 设置连接打开事件
    eventSource.onopen = (event) => {
      console.log('SSE连接已建立', event)
    }

    // 设置错误处理
    eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error)
      if (eventSource?.readyState === EventSource.CLOSED) {
        console.log('SSE连接已关闭')
      }
    }

    // 监听连接成功消息
    eventSource.addEventListener('connected', (event) => {
      console.debug('收到SSE连接确认:', event.data)
    })

    // 监听文档状态更新事件
    eventSource.addEventListener('document-status-update', (event) => {
      try {
        const statusUpdate = JSON.parse(event.data)
        handleSSEMessage({ type: 'document-status-update', data: statusUpdate })
      } catch (error) {
        console.error('解析文档状态更新失败:', error)
      }
    })

    // 等待连接建立
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('SSE连接超时'))
      }, 10000) // 10秒超时

      const onOpen = () => {
        clearTimeout(timeout)
        eventSource?.removeEventListener('open', onOpen)
        eventSource?.removeEventListener('error', onError)
        resolve(true)
      }

      const onError = () => {
        clearTimeout(timeout)
        eventSource?.removeEventListener('open', onOpen)
        eventSource?.removeEventListener('error', onError)
        reject(new Error('SSE连接失败'))
      }

      eventSource?.addEventListener('open', onOpen)
      eventSource?.addEventListener('error', onError)
    })

    console.log('SSE连接建立完成')

  } catch (error) {
    console.error('建立SSE连接失败:', error)
    if (eventSource) {
      eventSource.close()
      eventSource = null
    }
    throw error
  }
}

// 处理SSE消息
const handleSSEMessage = (data: any) => {
  // 根据消息类型处理
  if (data.type === 'document-status-update') {
    handleDocumentStatusUpdate(data.data)
  }
}

// 处理文档状态更新
const handleDocumentStatusUpdate = (statusUpdate: any) => {
  try {
    // 验证statusUpdate对象
    if (!statusUpdate || typeof statusUpdate !== 'object') {
      console.warn('无效的状态更新对象:', statusUpdate)
      return
    }

    // 使用智能匹配函数查找文件
    const file = configStore.uploadedFiles.find(f => f.documentId === statusUpdate.documentId)

    if (file) {
      // 安全地更新文件状态
      file.documentStatus = statusUpdate.status
      file.processProgress = statusUpdate.progress || 0

      // 安全地更新分段数量
      if (statusUpdate.segmentCount) {
        file.segmentCount = statusUpdate.segmentCount
      }

      // 更新整体进度
      try {
        const completedFiles = configStore.uploadedFiles.filter((f: any) =>
          f.documentStatus === 'segmented' || f.documentStatus === 'segment_error'
        ).length

        processedFiles.value = completedFiles
        processProgress.value = (completedFiles / configStore.uploadedFiles.length) * 100

        // 如果所有文件都处理完成，结束处理状态
        if (completedFiles === configStore.uploadedFiles.length) {
          processing.value = false

          // 生成处理结果
          const result = {
            totalChunks: configStore.uploadedFiles.reduce((sum: number, file: any) => sum + (file.segmentCount || 0), 0),
            processedFiles: configStore.uploadedFiles.length,
            processingTime: Date.now()
          }

          configStore.setProcessResult(result)
        }
      } catch (progressError) {
        console.error('更新整体进度时出错:', progressError)
      }
    }
  } catch (error) {
    console.error('处理文档状态更新时发生错误:', error, statusUpdate)
    // 不重新抛出错误，避免影响Vue的错误处理器
  }
}
// 关闭SSE连接
const closeSSE = () => {
  stopSSE()
}

// 停止SSE连接
const stopSSE = () => {
  if (eventSource) {
    eventSource.close()
    eventSource = null
  }
}

// 导出停止SSE连接的方法，供外部调用
defineExpose({
  stopSSE,
  closeSSE
})

// 根据当前批次加载上传文件列表
const loadCurBatchDocs = async () => {
  const queryDTO: DocumentQueryDTO = {
    current: 1,
    size: configStore.pageSize,
    batch: configStore.batchId
  }
  const response = await RagAPI.getDocumentPage(queryDTO)
  if (response.success && response.data && response.data.records) {
    response.data.records.forEach((file: any) => {
      file.size  = file.docMetadata?.file_size
    })
    configStore.setUploadedFiles(response.data.records)
  }
}

onMounted(async () => {
  // 从路由参数获取知识库信息
  const datasetId = route.query.datasetId as string || ''
  const batchId = route.query.batchId as string || uuidv4()
  const datasetName = route.query.name as string || '未命名知识库'

  // 初始化知识库创建流程
  configStore.initializeKnowledgeFlow(datasetId, datasetName, batchId)

  // 加载字典数据
  await queryDocumentStatusDictData()

  // 根据当前批次加载上传文件列表
  await loadCurBatchDocs()

  // 设置事件监听器
  setupEventListeners()

  // 初始化时清空所有配置，使用默认配置
  configStore.clearAllConfigs()
})

onUnmounted(() => {
  // 清理事件监听器
  eventCleanupFunctions.value.forEach(cleanup => cleanup())
  eventCleanupFunctions.value = []

  // 组件卸载时清理事件监听器
  eventBus.clearAllListeners()
  // 关闭SSE连接
  closeSSE()
})
</script>

<style scoped>
/* 页面头部 */
.page-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-top .next-step-btn {
  padding: 8px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
}

.header-top .next-step-btn:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.header-top .next-step-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

/* 上一步按钮样式 */
.header-top .prev-step-btn {
  padding: 8px 16px;
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
}

.header-top .prev-step-btn:hover:not(:disabled) {
  background: #f1f5f9;
  color: #475569;
  border-color: #94a3b8;
  transform: translateY(-1px);
}

.header-top .prev-step-btn:disabled {
  background: #f9fafb;
  color: #d1d5db;
  border-color: #e5e7eb;
  cursor: not-allowed;
  transform: none;
}

/* 保存按钮样式 */
.header-top .save-btn {
  padding: 8px 16px;
  background: #3693f3;
  color: #ffffff;
  border: 1px solid #2178f3;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
}

.header-top .save-btn:hover:not(:disabled) {
  background: #3d91e6;
  color: #ffffff;
  border-color: #2178f3;
  transform: translateY(-1px);
}

.header-top .save-btn:disabled {
  background: #f9fafb;
  color: #d1d5db;
  border-color: #e5e7eb;
  cursor: not-allowed;
  transform: none;
}


/* 流程步骤 */
.step-item {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid #e5e7eb;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

.step-item.active::after {
  border-left-color: #3b82f6;
}

.step-item.completed::after {
  border-left-color: #10b981;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-item.active .step-number {
  background: #3b82f6;
  color: white;
}

.step-item.completed .step-number {
  background: #10b981;
  color: white;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  margin-bottom: 2px;
}

.step-item.active .step-title {
  color: #1e40af;
}

.step-item.completed .step-title {
  color: #047857;
}

.step-desc {
  font-size: 12px;
  color: #6b7280;
}

/* 步骤内容区域 */

.step-panel {
  flex-grow: 0;
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  min-height: 130px;
}


/* 响应式设计 */
@media (max-width: 1024px) {
  .step-item::after {
    display: none;
  }
}
</style>
