package com.xhcai.modules.rag.entity;

import java.util.HashMap;
import java.util.Map;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识库分段配置实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "datasets_segment_config")
@TableName(value = "datasets_segment_config", autoResultMap = true)
@Schema(description = "知识库分段配置")
public class KnowledgeSegmentConfig extends BaseWithTenantIDEntity {

    /**
     * 分段配置
     */
    @Schema(description = "分段配置")
    @Column(name = "segment_config", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    @TableField(value = "segment_config", typeHandler = com.xhcai.modules.rag.handler.SegmentConfigTypeHandler.class, jdbcType = org.apache.ibatis.type.JdbcType.OTHER)
    private SegmentConfig segmentConfig;

    /**
     * 清洗配置
     */
    @Schema(description = "清洗配置")
    @Column(name = "cleaning_config", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    @TableField(value = "cleaning_config", typeHandler = com.xhcai.modules.rag.handler.CleaningConfigTypeHandler.class, jdbcType = org.apache.ibatis.type.JdbcType.OTHER)
    private CleaningConfig cleaningConfig;

    /**
     * 分段配置内部类
     */
    @Data
    @Schema(description = "分段配置")
    public static class SegmentConfig {

        @Schema(description = "分段类型：directory-目录切分， constantLength-固定长度切分，natural-自然文段切分，delimiter-分隔符切分，none-不切分")
        private String type;

        @Schema(description = "目录切分分段配置参数")
        private Map<String, Object> directory = new HashMap<>();

        @Schema(description = "自然文段切分分段配置参数")
        private Map<String, Object> natural = new HashMap<>();

        @Schema(description = "分隔符切分分段配置参数")
        private Map<String, Object> delimiter = new HashMap<>();

        @Schema(description = "固定长度切分分段配置参数")
        private Map<String, Object> constantLength = new HashMap<>();
    }

    /**
     * 清洗配置内部类
     */
    @Data
    @Schema(description = "清洗配置")
    public static class CleaningConfig {

        /**
         * 移除空行
         */
        @Schema(description = "移除空行")
        private Boolean removeEmptyLines;

        /**
         * 移除多余空格
         */
        @Schema(description = "移除多余空格")
        private Boolean removeExtraSpaces;

        /**
         * 移除特殊字符
         */
        @Schema(description = "移除特殊字符")
        private Boolean removeSpecialChars;

        /**
         * 文本标准化
         */
        @Schema(description = "文本标准化")
        private Boolean normalizeText;

        /**
         * 删除URL和邮箱
         */
        @Schema(description = "删除URL和邮箱")
        private Boolean deleteSymbol;

        /**
         * 删除内嵌多媒体
         */
        @Schema(description = "删除内嵌多媒体")
        private Boolean deleteInlineMedia;

        /**
         * 过滤关键词
         */
        @Schema(description = "过滤关键词")
        private String filterKeywords;
    }
}
