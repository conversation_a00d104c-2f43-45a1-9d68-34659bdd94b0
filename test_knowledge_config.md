# 知识库配置功能测试指南

## 功能概述
本次实现了以下功能：
1. 在知识库配置页面添加向量数据库和文件存储的默认选择配置项
2. 优化知识库创建页面，从默认配置获取向量化检索配置
3. 在知识库创建时保存选择的向量数据库和文件存储信息到datasets表

## 数据库变更
### 1. datasets_vectorization_config
- 添加了 `vector_database_id` 字段（VARCHAR(36)）
- 添加了 `file_storage_id` 字段（VARCHAR(36)）

### 2. datasets表
- 添加了 `vector_database_id` 字段（VARCHAR(36)）
- 添加了 `file_storage_id` 字段（VARCHAR(36)）

## 测试步骤

### 1. 测试知识库配置页面（KnowledgeConfiguration.vue）
1. 访问 `/settings/knowledge-configuration` 页面
2. 检查是否显示"默认向量数据库"选择框
3. 检查是否显示"默认文件存储"选择框
4. 选择向量数据库和文件存储配置
5. 点击"保存配置"按钮
6. 验证配置是否保存到 `datasets_vectorization_config` 表

### 2. 测试知识库创建页面（KnowledgeCreate.vue）
1. 访问 `/rag/knowledge/create` 页面
2. 检查向量化配置区域是否显示：
   - 向量数据库选择框（应该显示默认选择）
   - 文件存储选择框（应该显示默认选择）
3. 填写知识库基本信息
4. 选择不同的向量数据库和文件存储
5. 创建知识库
6. 验证 `datasets` 表中是否保存了选择的向量数据库和文件存储ID

### 3. API测试
#### 获取向量化配置
```bash
GET /api/rag/config/vectorization
```
应该返回包含 `vectorDatabaseId` 和 `fileStorageId` 的配置

#### 保存向量化配置
```bash
POST /api/rag/config/vectorization
Content-Type: application/json

{
  "indexMode": "high_quality",
  "embeddingModel": "text-embedding-ada-002",
  "vectorDatabaseId": "vector_db_id",
  "fileStorageId": "file_storage_id",
  "retrievalSettings": {
    "retrievalMode": "hybrid",
    "enableRerank": false,
    "topK": 5,
    "scoreThreshold": 0.7,
    "hybridWeights": {
      "semanticWeight": 0.7,
      "keywordWeight": 0.3
    }
  }
}
```

#### 创建知识库
```bash
POST /api/rag/datasets
Content-Type: application/json

{
  "name": "测试知识库",
  "description": "测试描述",
  "dataSourceType": "upload_file",
  "vectorDatabaseId": "vector_db_id",
  "fileStorageId": "file_storage_id",
  "vectorizationConfig": {
    "indexMode": "high_quality",
    "embeddingModel": "text-embedding-ada-002"
  }
}
```

## 验证点
1. 配置页面能正确加载向量数据库和文件存储列表
2. 配置能正确保存到数据库
3. 创建页面能从默认配置加载设置
4. 创建知识库时能正确保存向量数据库和文件存储ID
5. 前端界面显示正常，无JavaScript错误
6. 后端API响应正常，无服务器错误

## 可能的问题
1. 数据库迁移脚本未执行
2. 向量数据库或文件存储服务未配置
3. 权限问题导致API调用失败
4. 前端类型定义不匹配

## 回滚方案
如果出现问题，可以：
1. 回滚数据库迁移：删除新增的字段
2. 恢复原始的前端代码
3. 移除新增的API字段
